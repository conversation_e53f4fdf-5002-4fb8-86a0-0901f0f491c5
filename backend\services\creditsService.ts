// 积分服务 - 支持异步扣费和快速余额检查
import prisma from '../lib/prisma.js';
import { UsageType } from '@prisma/client';
import { logger } from '../utils/logger.js';

/**
 * 积分服务类 - 专门处理面试积分的检查和扣费逻辑
 * 核心设计理念：分离"检查"与"扣费"，支持异步处理
 */
export class CreditsService {
  private static instance: CreditsService;

  public static getInstance(): CreditsService {
    if (!CreditsService.instance) {
      CreditsService.instance = new CreditsService();
    }
    return CreditsService.instance;
  }

  /**
   * 【同步】快速检查用户模拟面试积分是否足够
   * 这个函数只执行一次数据库读取，速度极快，可以阻塞主流程。
   * @param userId - 用户ID
   * @returns 如果积分足够，返回 true，否则返回 false
   */
  public async hasEnoughCreditsForMockInterview(userId: string): Promise<boolean> {
    try {
      const userBalance = await prisma.userBalance.findUnique({
        where: { userId },
        select: { mockInterviewCredits: true },
      });

      if (!userBalance || userBalance.mockInterviewCredits < 1) {
        logger.info(`[CreditsService] User ${userId} has insufficient mock interview credits`);
        return false;
      }

      logger.info(`[CreditsService] User ${userId} has sufficient mock interview credits: ${userBalance.mockInterviewCredits}`);
      return true;
    } catch (error) {
      logger.error(`[CreditsService] Error checking credits for user ${userId}:`, error);
      return false; // 出错时保守处理，拒绝启动
    }
  }

  /**
   * 【异步】执行完整的模拟面试扣费数据库事务
   * 这个函数包含了多次数据库写入，我们不希望它阻塞面试流程，因此设计为可以独立、异步执行。
   * @param userId - 用户ID
   * @param sessionId - 面试会话ID，用于记录消费详情
   * @returns 返回一个 Promise，成功时 resolve，失败时 reject
   */
  public async recordCreditUsageForMockInterview(userId: string, sessionId: string): Promise<void> {
    try {
      logger.info(`[CreditsService] Starting async credit deduction for user ${userId}, session ${sessionId}`);

      // 使用Prisma事务确保数据一致性
      await prisma.$transaction(async (tx) => {
        // 1. 再次查找用户余额，确保安全（防止并发问题）
        const userBalance = await tx.userBalance.findUnique({
          where: { userId },
          select: { mockInterviewCredits: true, updatedAt: true }
        });

        if (!userBalance || userBalance.mockInterviewCredits < 1) {
          throw new Error(`积分不足，扣费事务中断。用户: ${userId}`);
        }

        // 2. 更新用户积分
        await tx.userBalance.update({
          where: { 
            userId,
            updatedAt: userBalance.updatedAt // 乐观锁
          },
          data: {
            mockInterviewCredits: {
              decrement: 1,
            },
          },
        });

        // 3. 创建一条消费记录
        await tx.usageRecord.create({
          data: {
            userId,
            type: UsageType.MOCK_INTERVIEW,
            amount: -1, // 负数表示消耗
            reason: `AI模拟面试消耗: ${sessionId}`,
          },
        });
      });

      logger.info(`[CreditsService] Async credit deduction SUCCESS for user: ${userId}, session: ${sessionId}`);
    } catch (error) {
      // 如果异步扣费失败，将错误抛出，以便上层调用者可以捕获并记录
      logger.error(`[CreditsService] Async credit deduction FAILED for user: ${userId}, session: ${sessionId}`, error);
      throw error;
    }
  }

  /**
   * 【同步】快速检查用户正式面试积分是否足够
   * @param userId - 用户ID
   * @returns 如果积分足够，返回 true，否则返回 false
   */
  public async hasEnoughCreditsForFormalInterview(userId: string): Promise<boolean> {
    try {
      const userBalance = await prisma.userBalance.findUnique({
        where: { userId },
        select: { formalInterviewCredits: true },
      });

      if (!userBalance || userBalance.formalInterviewCredits < 1) {
        logger.info(`[CreditsService] User ${userId} has insufficient formal interview credits`);
        return false;
      }

      logger.info(`[CreditsService] User ${userId} has sufficient formal interview credits: ${userBalance.formalInterviewCredits}`);
      return true;
    } catch (error) {
      logger.error(`[CreditsService] Error checking formal credits for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * 【异步】执行完整的正式面试扣费数据库事务
   * @param userId - 用户ID
   * @param sessionId - 面试会话ID
   * @returns 返回一个 Promise，成功时 resolve，失败时 reject
   */
  public async recordCreditUsageForFormalInterview(userId: string, sessionId: string): Promise<void> {
    try {
      logger.info(`[CreditsService] Starting async formal credit deduction for user ${userId}, session ${sessionId}`);

      await prisma.$transaction(async (tx) => {
        const userBalance = await tx.userBalance.findUnique({
          where: { userId },
          select: { formalInterviewCredits: true, updatedAt: true }
        });

        if (!userBalance || userBalance.formalInterviewCredits < 1) {
          throw new Error(`正式面试积分不足，扣费事务中断。用户: ${userId}`);
        }

        await tx.userBalance.update({
          where: { 
            userId,
            updatedAt: userBalance.updatedAt
          },
          data: {
            formalInterviewCredits: {
              decrement: 1,
            },
          },
        });

        await tx.usageRecord.create({
          data: {
            userId,
            type: UsageType.FORMAL_INTERVIEW,
            amount: -1,
            reason: `AI正式面试消耗: ${sessionId}`,
          },
        });
      });

      logger.info(`[CreditsService] Async formal credit deduction SUCCESS for user: ${userId}, session: ${sessionId}`);
    } catch (error) {
      logger.error(`[CreditsService] Async formal credit deduction FAILED for user: ${userId}, session: ${sessionId}`, error);
      throw error;
    }
  }
}

// 导出单例实例
export const creditsService = CreditsService.getInstance();
